"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import {
  User,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updateProfile
} from 'firebase/auth'
import { doc, setDoc } from 'firebase/firestore'
import { auth, db } from '@/lib/firebase'
import { getAuthErrorMessage, logAuthError, authRateLimiter } from './error-handler'
import {
  sanitizeEmail,
  validatePasswordStrength,
  logSecurityEvent,
  ClientRateLimiter
} from './security'

export interface UserRole {
  role: 'admin' | 'user'
  email: string
  uid: string
  displayName?: string
  createdAt?: string
}

interface AuthContextType {
  user: User | null
  userRole: UserRole | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, displayName?: string) => Promise<void>
  logout: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  refreshUserRole: (forceRefresh?: boolean) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)
  const [lastActivity, setLastActivity] = useState(Date.now())

  // Rate limiters for different operations
  const loginRateLimiter = new ClientRateLimiter(5, 15 * 60 * 1000) // 5 attempts per 15 minutes
  const signupRateLimiter = new ClientRateLimiter(3, 60 * 60 * 1000) // 3 attempts per hour

  const refreshUserRole = async (forceRefresh = false) => {
    if (!user) {
      setUserRole(null)
      return
    }

    try {
      // Get the user's ID token to access custom claims
      // Force refresh if requested to get updated custom claims
      const idTokenResult = await user.getIdTokenResult(forceRefresh)
      const role = idTokenResult.claims.role as 'admin' | 'user' || 'user'

      console.log('Token claims:', idTokenResult.claims)
      console.log('User role from token:', role)

      setUserRole({
        role,
        email: user.email!,
        uid: user.uid,
        displayName: user.displayName || undefined,
        createdAt: idTokenResult.claims.createdAt as string || undefined
      })
    } catch (error) {
      console.error('Error fetching user role:', error)
      // Default to user role if there's an error
      setUserRole({
        role: 'user',
        email: user.email!,
        uid: user.uid,
        displayName: user.displayName || undefined
      })
    }
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setUser(user)
      if (user) {
        await refreshUserRole()
      } else {
        setUserRole(null)
      }
      setLoading(false)
    })

    return unsubscribe
  }, [])

  // Refresh user role when user changes
  useEffect(() => {
    if (user) {
      refreshUserRole()
    }
  }, [user])

  const signIn = async (email: string, password: string) => {
    // Rate limiting check
    if (!loginRateLimiter.canAttempt()) {
      const timeUntilReset = Math.ceil(loginRateLimiter.getTimeUntilReset() / 1000 / 60)
      throw new Error(`Too many login attempts. Please try again in ${timeUntilReset} minutes.`)
    }

    try {
      // Sanitize input
      const sanitizedEmail = sanitizeEmail(email)

      // Record attempt
      loginRateLimiter.recordAttempt()

      const result = await signInWithEmailAndPassword(auth, sanitizedEmail, password)

      // Log successful login
      logSecurityEvent({
        type: 'login',
        userId: result.user.uid,
        email: sanitizedEmail,
      })

      setLastActivity(Date.now())
    } catch (error: any) {
      // Log failed login attempt
      logSecurityEvent({
        type: 'failed_login',
        email: email,
        details: { errorCode: error.code }
      })

      logAuthError(error, 'signIn')
      throw new Error(getAuthErrorMessage(error))
    }
  }

  const signUp = async (email: string, password: string, displayName?: string) => {
    // Rate limiting check
    if (!signupRateLimiter.canAttempt()) {
      const timeUntilReset = Math.ceil(signupRateLimiter.getTimeUntilReset() / 1000 / 60)
      throw new Error(`Too many signup attempts. Please try again in ${timeUntilReset} minutes.`)
    }

    try {
      // Validate password strength
      const passwordValidation = validatePasswordStrength(password)
      if (!passwordValidation.isValid) {
        throw new Error(passwordValidation.feedback[0] || 'Password does not meet requirements')
      }

      // Sanitize input
      const sanitizedEmail = sanitizeEmail(email)
      const sanitizedDisplayName = displayName ? displayName.trim().slice(0, 100) : undefined

      // Record attempt
      signupRateLimiter.recordAttempt()

      const result = await createUserWithEmailAndPassword(auth, sanitizedEmail, password)

      if (sanitizedDisplayName && result.user) {
        await updateProfile(result.user, { displayName: sanitizedDisplayName })
      }

      // Create user document in Firestore
      if (result.user) {
        await setDoc(doc(db, 'users', result.user.uid), {
          uid: result.user.uid,
          email: sanitizedEmail,
          displayName: sanitizedDisplayName || null,
          role: 'user',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
      }

      // Log successful signup
      logSecurityEvent({
        type: 'login', // First login after signup
        userId: result.user.uid,
        email: sanitizedEmail,
      })

      setLastActivity(Date.now())
    } catch (error: any) {
      logAuthError(error, 'signUp')
      throw new Error(getAuthErrorMessage(error))
    }
  }

  const logout = async () => {
    try {
      // Log logout event
      if (user) {
        logSecurityEvent({
          type: 'logout',
          userId: user.uid,
          email: user.email || undefined,
        })
      }

      await signOut(auth)
    } catch (error: any) {
      logAuthError(error, 'logout')
      throw new Error(getAuthErrorMessage(error))
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const sanitizedEmail = sanitizeEmail(email)
      await sendPasswordResetEmail(auth, sanitizedEmail)
    } catch (error: any) {
      logAuthError(error, 'resetPassword')
      throw new Error(getAuthErrorMessage(error))
    }
  }

  const value: AuthContextType = {
    user,
    userRole,
    loading,
    signIn,
    signUp,
    logout,
    resetPassword,
    refreshUserRole
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
