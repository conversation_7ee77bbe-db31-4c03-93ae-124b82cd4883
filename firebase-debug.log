[debug] [2025-06-18T09:17:02.579Z] ----------------------------------------------------------------------
[debug] [2025-06-18T09:17:02.581Z] Command:       /home/<USER>/.nvm/versions/node/v22.12.0/bin/node /home/<USER>/.nvm/versions/node/v22.12.0/bin/firebase functions:shell
[debug] [2025-06-18T09:17:02.581Z] CLI Version:   14.2.2
[debug] [2025-06-18T09:17:02.581Z] Platform:      linux
[debug] [2025-06-18T09:17:02.581Z] Node Version:  v22.12.0
[debug] [2025-06-18T09:17:02.582Z] Time:          Wed Jun 18 2025 14:47:02 GMT+0530 (India Standard Time)
[debug] [2025-06-18T09:17:02.582Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-18T09:17:02.782Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-18T09:17:02.782Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-18T09:17:02.782Z] [iam] checking project agnextech for permissions ["firebase.projects.get"]
[debug] [2025-06-18T09:17:02.783Z] Checked if tokens are valid: true, expires at: 1750239131411
[debug] [2025-06-18T09:17:02.783Z] Checked if tokens are valid: true, expires at: 1750239131411
[debug] [2025-06-18T09:17:02.784Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/agnextech:testIamPermissions [none]
[debug] [2025-06-18T09:17:02.784Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/agnextech:testIamPermissions x-goog-quota-user=projects/agnextech
[debug] [2025-06-18T09:17:02.784Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/agnextech:testIamPermissions {"permissions":["firebase.projects.get"]}
[debug] [2025-06-18T09:17:04.172Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/agnextech:testIamPermissions 200
[debug] [2025-06-18T09:17:04.172Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/agnextech:testIamPermissions {"permissions":["firebase.projects.get"]}
[debug] [2025-06-18T09:17:04.175Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[warn] ⚠  functions: Application Default Credentials detected. Non-emulated services will access production using these credentials. Be careful! {"metadata":{"emulator":{"name":"functions"},"message":"Application Default Credentials detected. Non-emulated services will access production using these credentials. Be careful!"}}
[debug] [2025-06-18T09:17:07.478Z] Checked if tokens are valid: true, expires at: 1750239131411
[debug] [2025-06-18T09:17:07.478Z] Checked if tokens are valid: true, expires at: 1750239131411
[debug] [2025-06-18T09:17:07.478Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/agnextech/adminSdkConfig [none]
[debug] [2025-06-18T09:17:08.033Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/agnextech/adminSdkConfig 200
[debug] [2025-06-18T09:17:08.033Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/agnextech/adminSdkConfig {"projectId":"agnextech","databaseURL":"https://agnextech-default-rtdb.asia-southeast1.firebasedatabase.app","storageBucket":"agnextech.firebasestorage.app"}
[debug] [2025-06-18T09:17:08.051Z] [functions] Watching "/home/<USER>/Documents/deskp/agnex/functions" for Cloud Functions...
[debug] [2025-06-18T09:17:08.055Z] Validating nodejs source
[debug] [2025-06-18T09:17:08.625Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "firebase-admin": "^12.7.0",
    "firebase-functions": "^6.0.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0",
    "typescript": "^4.9.0"
  },
  "private": true
}
[debug] [2025-06-18T09:17:08.626Z] Building nodejs source
[debug] [2025-06-18T09:17:08.626Z] Failed to find version of module node: reached end of search path /home/<USER>/Documents/deskp/agnex/functions/node_modules
[info] ✔  functions: Using node@22 from host. 
[debug] [2025-06-18T09:17:08.628Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-06-18T09:17:08.630Z] Found firebase-functions binary at '/home/<USER>/Documents/deskp/agnex/functions/node_modules/.bin/firebase-functions'
[info] Serving at port 8103

[debug] [2025-06-18T09:17:08.977Z] Got response from /__/functions.yaml {"endpoints":{"setUserRole":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"setUserRole"},"getAllUsers":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"callableTrigger":{},"entryPoint":"getAllUsers"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[debug] [2025-06-18T09:17:12.998Z] [functions] Loaded functions definitions from source: setUserRole, getAllUsers.
[debug] [2025-06-18T09:17:12.998Z] [functions[us-central1-setUserRole]] http function initialized (http://127.0.0.1:5000/agnextech/us-central1/setUserRole).
[debug] [2025-06-18T09:17:12.998Z] [functions[us-central1-getAllUsers]] http function initialized (http://127.0.0.1:5000/agnextech/us-central1/getAllUsers).
[info] i  functions: Loaded functions: setUserRole, getAllUsers 
[warn] ⚠  functions: The following emulators are not running, calls to these services will affect production: firestore, database, pubsub, storage, eventarc, tasks 
