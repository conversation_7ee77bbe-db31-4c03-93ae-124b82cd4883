#!/usr/bin/env node

/**
 * <PERSON>ript to promote a user to admin role
 * This script bypasses the cloud function restrictions and directly sets custom claims
 * Usage: node promote-user-to-admin.js <email>
 * Example: node promote-user-to-admin.js <EMAIL>
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccount = require('./serviceAccount.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: `https://${serviceAccount.project_id}-default-rtdb.firebaseio.com`
});

const auth = admin.auth();
const db = admin.firestore();

async function promoteUserToAdmin(email) {
  try {
    console.log(`🔍 Looking for user with email: ${email}`);
    
    // Get user by email
    const userRecord = await auth.getUserByEmail(email);
    console.log(`✅ Found user: ${userRecord.uid}`);
    
    // Set custom claims to make user admin
    await auth.setCustomUserClaims(userRecord.uid, { role: 'admin' });
    console.log(`✅ Set custom claims for user: ${userRecord.uid}`);
    
    // Update user document in Firestore
    const userDocRef = db.collection('users').doc(userRecord.uid);
    const userDoc = await userDocRef.get();
    
    if (userDoc.exists) {
      // Update existing document
      await userDocRef.update({
        role: 'admin',
        updatedAt: new Date().toISOString(),
        updatedBy: 'system-script'
      });
      console.log(`✅ Updated existing user document in Firestore`);
    } else {
      // Create new document
      await userDocRef.set({
        uid: userRecord.uid,
        email: userRecord.email,
        displayName: userRecord.displayName || null,
        role: 'admin',
        createdAt: userRecord.metadata.creationTime,
        updatedAt: new Date().toISOString(),
        updatedBy: 'system-script'
      });
      console.log(`✅ Created new user document in Firestore`);
    }
    
    console.log(`🎉 Successfully promoted ${email} to admin!`);
    console.log(`📝 User will need to sign out and sign back in for changes to take effect.`);
    
  } catch (error) {
    console.error(`❌ Error promoting user to admin:`, error.message);
    
    if (error.code === 'auth/user-not-found') {
      console.log(`💡 User with email ${email} not found. Make sure they have signed up first.`);
    } else if (error.code === 'auth/invalid-email') {
      console.log(`💡 Invalid email format: ${email}`);
    }
    
    process.exit(1);
  }
}

async function listAllUsers() {
  try {
    console.log(`📋 Listing all users:`);
    const listUsersResult = await auth.listUsers();
    
    if (listUsersResult.users.length === 0) {
      console.log(`📭 No users found.`);
      return;
    }
    
    listUsersResult.users.forEach((userRecord, index) => {
      const role = userRecord.customClaims?.role || 'user';
      console.log(`${index + 1}. ${userRecord.email} (${userRecord.uid}) - Role: ${role}`);
    });
    
  } catch (error) {
    console.error(`❌ Error listing users:`, error.message);
    process.exit(1);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`📖 Usage: node promote-user-to-admin.js <email>`);
    console.log(`📖 Or: node promote-user-to-admin.js --list (to list all users)`);
    console.log(`📖 Example: node promote-user-to-admin.js <EMAIL>`);
    process.exit(1);
  }
  
  if (args[0] === '--list' || args[0] === '-l') {
    await listAllUsers();
    process.exit(0);
  }
  
  const email = args[0];
  
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    console.error(`❌ Invalid email format: ${email}`);
    process.exit(1);
  }
  
  await promoteUserToAdmin(email);
  process.exit(0);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the script
main();
