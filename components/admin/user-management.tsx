"use client"

import { useState, useEffect } from 'react'
import { Shield, User<PERSON>heck, Users, Loader2, MoreHorizontal, AlertTriangle, RefreshCw } from 'lucide-react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { getAllUsers, setUserRole, UserData, formatDate } from '@/lib/auth/auth-utils'
import { useAuth } from '@/lib/auth/auth-context'

export function UserManagement() {
  const [users, setUsers] = useState<UserData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null)
  const [isUpdatingRole, setIsUpdatingRole] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [pendingRoleChange, setPendingRoleChange] = useState<{
    user: UserData
    newRole: 'admin' | 'user'
  } | null>(null)

  const { userRole, refreshUserRole, user } = useAuth()

  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError('')

      // Debug: Log current user and role
      console.log('Current user:', user)
      console.log('Current userRole:', userRole)
      console.log('User authenticated:', !!user)
      console.log('User UID:', user?.uid)
      console.log('User email:', user?.email)

      // Check if user is authenticated
      if (!user) {
        setError('You must be signed in to view users')
        return
      }

      // Check if user has admin role
      if (!userRole || userRole.role !== 'admin') {
        setError('Admin privileges required to view users')
        return
      }

      console.log('Calling getAllUsers function...')

      const result = await getAllUsers()
      console.log('getAllUsers result:', result)
      setUsers(result.data.users)
    } catch (error: any) {
      console.error('Error fetching users:', error)
      setError(error.message || 'Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  const handleRoleChange = async (user: UserData, newRole: 'admin' | 'user') => {
    if (user.role === newRole) return

    setPendingRoleChange({ user, newRole })
    setShowConfirmDialog(true)
  }

  const confirmRoleChange = async () => {
    if (!pendingRoleChange) return

    try {
      setIsUpdatingRole(true)
      await setUserRole({ uid: pendingRoleChange.user.uid, role: pendingRoleChange.newRole })
      
      // Update local state
      setUsers(users.map(u => 
        u.uid === pendingRoleChange.user.uid 
          ? { ...u, role: pendingRoleChange.newRole }
          : u
      ))
      
      setShowConfirmDialog(false)
      setPendingRoleChange(null)
    } catch (error: any) {
      setError(error.message || 'Failed to update user role')
    } finally {
      setIsUpdatingRole(false)
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    return role === 'admin' ? 'destructive' : 'secondary'
  }

  const getRoleIcon = (role: string) => {
    return role === 'admin' ? Shield : UserCheck
  }

  const refreshUserRoles = async () => {
    try {
      setLoading(true)
      setError('')
      // Force refresh the current user's token to get updated claims
      await refreshUserRole(true)
      // Then fetch all users again
      await fetchUsers()
    } catch (error: any) {
      setError(error.message || 'Failed to refresh user roles')
    } finally {
      setLoading(false)
    }
  }

  const testAuthentication = async () => {
    try {
      console.log('=== Authentication Test ===')
      console.log('User object:', user)
      console.log('User role:', userRole)

      if (user) {
        const token = await user.getIdToken()
        console.log('ID Token (first 50 chars):', token.substring(0, 50) + '...')

        const tokenResult = await user.getIdTokenResult()
        console.log('Token claims:', tokenResult.claims)
        console.log('Token auth time:', tokenResult.authTime)
        console.log('Token issued at:', tokenResult.issuedAtTime)
        console.log('Token expires at:', tokenResult.expirationTime)
      } else {
        console.log('No user found!')
      }
    } catch (error) {
      console.error('Authentication test error:', error)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Loading users...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              User Management
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={testAuthentication}
              >
                Test Auth
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={refreshUserRoles}
                disabled={loading || isUpdatingRole}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Roles
              </Button>
            </div>
          </div>
          <CardDescription>
            Manage user roles and permissions. Only administrators can modify user roles.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Debug info */}
          <div className="mb-4 p-3 text-sm bg-blue-50 border border-blue-200 rounded-md">
            <strong>Debug Info:</strong><br/>
            User: {user ? `${user.email} (${user.uid})` : 'Not signed in'}<br/>
            Role: {userRole ? userRole.role : 'No role'}<br/>
            Admin: {userRole?.role === 'admin' ? 'Yes' : 'No'}
          </div>

          {error && (
            <div className="mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className="w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => {
                  const RoleIcon = getRoleIcon(user.role)
                  return (
                    <TableRow key={user.uid}>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {user.displayName || 'No name'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {user.email}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getRoleBadgeVariant(user.role)} className="flex items-center gap-1 w-fit">
                          <RoleIcon className="h-3 w-3" />
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatDate(user.createdAt)}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              disabled={user.role === 'admin' || !userRole || userRole.role !== 'admin'}
                              onClick={() => handleRoleChange(user, 'admin')}
                            >
                              <Shield className="h-4 w-4 mr-2" />
                              Make Admin
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              disabled={user.role === 'user' || !userRole || userRole.role !== 'admin'}
                              onClick={() => handleRoleChange(user, 'user')}
                            >
                              <UserCheck className="h-4 w-4 mr-2" />
                              Make User
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </div>

          {users.length === 0 && !loading && (
            <div className="text-center py-8 text-muted-foreground">
              No users found
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Details Dialog */}
      <Dialog open={!!selectedUser} onOpenChange={() => setSelectedUser(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Detailed information about the selected user
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Name</label>
                <p className="text-sm text-muted-foreground">
                  {selectedUser.displayName || 'No name provided'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium">Email</label>
                <p className="text-sm text-muted-foreground">{selectedUser.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Role</label>
                <div className="mt-1">
                  <Badge variant={getRoleBadgeVariant(selectedUser.role)}>
                    {selectedUser.role}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">User ID</label>
                <p className="text-sm text-muted-foreground font-mono">{selectedUser.uid}</p>
              </div>
              <div>
                <label className="text-sm font-medium">Created</label>
                <p className="text-sm text-muted-foreground">
                  {formatDate(selectedUser.createdAt)}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium">Last Login</label>
                <p className="text-sm text-muted-foreground">
                  {selectedUser.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : 'Never'}
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Role Change Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Role Change</DialogTitle>
            <DialogDescription>
              Are you sure you want to change this user's role? This action can be reversed later.
            </DialogDescription>
          </DialogHeader>
          <div className="flex space-x-2 justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setShowConfirmDialog(false)
                setPendingRoleChange(null)
              }}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              disabled={isUpdatingRole}
              onClick={confirmRoleChange}
            >
              {isUpdatingRole ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Confirm'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
